import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/priest/application/priest_provider.dart';
import 'package:shivish/apps/priest/priest_routes.dart';
import 'package:shivish/shared/core/auth/models/register_request.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:shivish/shared/ui_components/inputs/text_field.dart';
import 'package:shivish/shared/ui_components/media/image_picker_button.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/buttons/app_button.dart';
import 'package:shivish/shared/widgets/auth/email_verification_widget.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';


class PriestRegisterScreen extends ConsumerStatefulWidget {
  const PriestRegisterScreen({super.key});

  @override
  ConsumerState<PriestRegisterScreen> createState() => _PriestRegisterScreenState();
}

class _PriestRegisterScreenState extends ConsumerState<PriestRegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _experienceYearsController = TextEditingController();
  final _specializationsController = TextEditingController();
  final _serviceAreasController = TextEditingController();
  String? _profileImagePath;
  String? _qualificationProofPath;
  File? _aadharDocument;
  bool _isLoading = false;
  bool _isCheckingDocumentRequirements = true;
  bool _documentsRequired = false;
  bool _isEmailVerified = false;
  String? _errorMessage;

  late final DatabaseService _databaseService;

  @override
  void initState() {
    super.initState();
    _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
    _checkDocumentRequirements();
  }

  Future<void> _checkDocumentRequirements() async {
    setState(() {
      _isCheckingDocumentRequirements = true;
    });

    try {
      // Try to fetch from hybrid database
      try {
        final configData = await _databaseService.find('system_config', 'registration');

        if (configData != null) {
          final bool docsRequired = configData['documents_required'] == true;
          debugPrint('Database documents_required: $docsRequired');

          setState(() {
            _documentsRequired = docsRequired;
            _isCheckingDocumentRequirements = false;
          });
        } else {
          debugPrint('Registration config document does not exist in database');
          setState(() {
            _documentsRequired = false;
            _isCheckingDocumentRequirements = false;
          });
        }
      } catch (databaseError) {
        debugPrint('Error checking database: $databaseError');
        setState(() {
          _documentsRequired = false;
          _isCheckingDocumentRequirements = false;
        });
      }
    } catch (e) {
      debugPrint('Error in _checkDocumentRequirements: $e');
      setState(() {
        _documentsRequired = false;
        _isCheckingDocumentRequirements = false;
      });
    }
  }

  // Pick document file
  Future<void> _pickDocument(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          if (type == 'aadhar') {
            _aadharDocument = file;
          }
        });
      }
    } catch (e) {
      debugPrint('Error picking $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _experienceYearsController.dispose();
    _specializationsController.dispose();
    _serviceAreasController.dispose();
    super.dispose();
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) return;

    // Check email verification first
    if (!_isEmailVerified) {
      setState(() {
        _errorMessage = 'Please verify your email address first';
      });
      return;
    }

    // Only check for documents if documents are required
    if (_documentsRequired) {
      if (_qualificationProofPath == null) {
        setState(() {
          _errorMessage = 'Please upload your qualification proof';
        });
        return;
      }

      if (_aadharDocument == null) {
        setState(() {
          _errorMessage = 'Please upload your Aadhar document';
        });
        return;
      }
    }

    // Get the current state
    final currentState = ref.read(priestProvider);

    // Check if already loading
    if (currentState.isLoading) return;

    // Set loading state
    ref.read(priestProvider.notifier).clearError();
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create metadata map
      Map<String, dynamic> metadata = {
        // Include profile image regardless of document requirements
        if (_profileImagePath != null) 'profileImage': _profileImagePath,
        'specializations': _specializationsController.text
            .split(',')
            .map((e) => e.trim())
            .toList(),
        'serviceAreas': _serviceAreasController.text
            .split(',')
            .map((e) => e.trim())
            .toList(),
        'experienceYears': _experienceYearsController.text,
        'description': 'Priest with ${_experienceYearsController.text} years of experience',
      };

      // Only include documents if documents are required
      if (_documentsRequired) {
        if (_qualificationProofPath != null) {
          metadata['qualification_proof'] = _qualificationProofPath;
        }

        if (_aadharDocument != null) {
          // For Aadhar document, we'll store the file path for now
          // In a real app, you would upload this to Firebase Storage
          metadata['aadhar_document'] = _aadharDocument!.path;
        }
      }

      final request = RegisterRequest(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        displayName: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        role: UserRole.priest,
        metadata: metadata,
      );

      // Register the priest using the PriestNotifier
      await ref.read(priestProvider.notifier).register(request);

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Registration successful! Your account is pending approval.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Wait a moment for the snackbar to be visible
      await Future.delayed(const Duration(milliseconds: 1000));

      if (!mounted) return;

      // Navigate to pending approval screen
      context.go(PriestRoutes.pendingApproval);
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
      });
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: AppBar(
            title: const Text('Priest Registration'),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () {
                // Navigate back to login screen
                PriestRoutes.navigateToLogin(context);
              },
            ),
          ),
          body: _isCheckingDocumentRequirements
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16.0),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(bottom: 8.0),
                              child: Text(
                                'Profile Picture',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ),
                            ImagePickerButton(
                              onImageSelected: (image) {
                                setState(() {
                                  _profileImagePath = image.path;
                                });
                              },
                            ),
                          ],
                        ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _nameController,
                    label: 'Full Name',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  EmailVerificationWidget(
                    emailController: _emailController,
                    onVerificationChanged: (isVerified) {
                      setState(() {
                        _isEmailVerified = isVerified;
                      });
                    },
                    primaryColor: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _phoneController,
                    label: 'Phone Number',
                    keyboardType: TextInputType.phone,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your phone number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _passwordController,
                    label: 'Password',
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a password';
                      }
                      if (value.length < 6) {
                        return 'Password must be at least 6 characters';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _confirmPasswordController,
                    label: 'Confirm Password',
                    obscureText: true,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please confirm your password';
                      }
                      if (value != _passwordController.text) {
                        return 'Passwords do not match';
                      }
                      return null;
                    },
                  ),
                  // Only show document upload section if documents are required
                  if (_documentsRequired) ...[
                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),
                    const Text(
                      'Required Documents',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Please upload the following documents to complete your registration.',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Qualification Proof Document',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        ImagePickerButton(
                          onImageSelected: (image) {
                            setState(() {
                              _qualificationProofPath = image.path;
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Aadhar Card Document',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                        InkWell(
                          onTap: () => _pickDocument('aadhar'),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.upload_file, color: Colors.green),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    _aadharDocument != null
                                        ? _aadharDocument!.path.split('/').last
                                        : 'Upload Aadhar Card',
                                    style: TextStyle(
                                      color: _aadharDocument != null ? Colors.black : Colors.grey[600],
                                    ),
                                  ),
                                ),
                                if (_aadharDocument != null)
                                  const Icon(Icons.check_circle, color: Colors.green),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _experienceYearsController,
                    label: 'Years of Experience',
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your years of experience';
                      }
                      if (int.tryParse(value) == null) {
                        return 'Please enter a valid number';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _specializationsController,
                    label: 'Specializations (comma-separated)',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your specializations';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  AppTextField(
                    controller: _serviceAreasController,
                    label: 'Service Areas (comma-separated)',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your service areas';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  if (_errorMessage != null)
                    Container(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade300),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: Colors.red.shade700),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  AppButton(
                    onPressed: _handleRegister,
                    isLoading: _isLoading,
                    child: const Text('Register'),
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
        if (_isLoading) const LoadingIndicator(),
      ],
    );
  }
}
