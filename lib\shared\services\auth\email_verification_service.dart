import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../database/services/database_service.dart';
import '../../database/config/database_config.dart';

/// Shared email verification service for all apps
class EmailVerificationService {
  static final EmailVerificationService _instance = EmailVerificationService._internal();
  factory EmailVerificationService() => _instance;
  EmailVerificationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  late final DatabaseService _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());

  /// Send email verification for registration
  Future<void> sendEmailVerificationForRegistration(String email) async {
    try {
      debugPrint('EmailVerificationService: Sending verification to $email');

      // Check if email already exists in any user table
      final existingUsers = await _databaseService.getAll('users');
      final emailExists = existingUsers.any((user) => user['email'] == email);
      
      if (emailExists) {
        throw Exception('An account with this email already exists');
      }

      // Send OTP to email for verification
      await _supabase.auth.signInWithOtp(
        email: email,
      );

      debugPrint('EmailVerificationService: Verification email sent successfully');
    } catch (e) {
      debugPrint('EmailVerificationService: Error sending verification: $e');
      rethrow;
    }
  }

  /// Verify email OTP during registration
  Future<bool> verifyEmailOTPForRegistration(String email, String otp) async {
    try {
      debugPrint('EmailVerificationService: Verifying OTP for $email');

      final response = await _supabase.auth.verifyOTP(
        type: OtpType.email,
        email: email,
        token: otp,
      );

      if (response.user != null) {
        // Sign out immediately as this is just for verification
        await _supabase.auth.signOut();
        
        debugPrint('EmailVerificationService: Email verification successful');
        return true;
      } else {
        throw Exception('Invalid verification code');
      }
    } catch (e) {
      debugPrint('EmailVerificationService: Error verifying OTP: $e');
      return false;
    }
  }

  /// Check if email is already registered
  Future<bool> isEmailRegistered(String email) async {
    try {
      final existingUsers = await _databaseService.getAll('users');
      return existingUsers.any((user) => user['email'] == email);
    } catch (e) {
      debugPrint('EmailVerificationService: Error checking email: $e');
      return false;
    }
  }

  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }
}
