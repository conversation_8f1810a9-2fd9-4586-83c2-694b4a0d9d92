import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/widgets/traditional/traditional_pattern_widget.dart';
import '../../../../shared/theme/traditional_colors.dart';
import '../../../../shared/models/temple/temple_model.dart';
import '../../providers/temple_auth_provider.dart';
import '../../../../shared/widgets/auth/email_verification_widget.dart';

class TempleRegisterScreen extends ConsumerStatefulWidget {
  const TempleRegisterScreen({super.key});

  @override
  ConsumerState<TempleRegisterScreen> createState() =>
      _TempleRegisterScreenState();
}

class _TempleRegisterScreenState extends ConsumerState<TempleRegisterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _formKey = GlobalKey<FormState>();

  // Controllers for form fields
  final _templeNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _websiteController = TextEditingController();
  final _managerNameController = TextEditingController();
  final _managerEmailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  TempleType _selectedType = TempleType.local;
  List<String> _selectedDeities = [];
  List<String> _selectedFacilities = [];
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isEmailVerified = false;

  final List<String> _availableDeities = [
    'Lord Vishnu',
    'Lord Shiva',
    'Lord Krishna',
    'Lord Rama',
    'Goddess Durga',
    'Goddess Lakshmi',
    'Lord Ganesha',
    'Lord Hanuman',
    'Goddess Saraswati',
    'Lord Murugan',
    'Goddess Kali',
    'Lord Brahma',
  ];

  final List<String> _availableFacilities = [
    'Parking',
    'Prasadam Counter',
    'Donation Counter',
    'Shoe Stand',
    'Drinking Water',
    'Restrooms',
    'Wheelchair Access',
    'Audio System',
    'CCTV Security',
    'First Aid',
    'Guest House',
    'Library',
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _templeNameController.dispose();
    _descriptionController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _managerNameController.dispose();
    _managerEmailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(gradient: TraditionalColors.templeGradient),
        child: Stack(
          children: [
            // Traditional pattern background
            Positioned.fill(
              child: TraditionalPatternWidget(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                opacity: 0.1,
                color: Colors.white,
              ),
            ),
            SafeArea(
              child: Column(
                children: [
                  _buildHeader(),
                  _buildTabBar(),
                  Expanded(child: _buildTabContent()),
                  _buildNavigationButtons(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => context.pop(),
                icon: const Icon(Icons.arrow_back, color: Colors.white),
              ),
              const Expanded(
                child: Text(
                  'Register Temple',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(width: 48), // Balance the back button
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'Join our sacred platform',
            style: TextStyle(fontSize: 16, color: Colors.white70),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: TraditionalColors.templeOrange,
        unselectedLabelColor: Colors.grey,
        indicatorColor: TraditionalColors.templeOrange,
        indicatorSize: TabBarIndicatorSize.tab,
        tabs: const [
          Tab(text: 'Temple Info'),
          Tab(text: 'Details'),
          Tab(text: 'Account'),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildTempleInfoTab(),
            _buildDetailsTab(),
            _buildAccountTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildTempleInfoTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Basic Temple Information',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          TextFormField(
            controller: _templeNameController,
            decoration: const InputDecoration(
              labelText: 'Temple Name *',
              hintText: 'Enter temple name',
              prefixIcon: Icon(Icons.temple_hindu),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Temple name is required';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _descriptionController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Description *',
              hintText: 'Brief description of the temple',
              prefixIcon: Icon(Icons.description),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Description is required';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          DropdownButtonFormField<TempleType>(
            value: _selectedType,
            decoration: const InputDecoration(
              labelText: 'Temple Type *',
              prefixIcon: Icon(Icons.category),
            ),
            items: TempleType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_getTypeLabel(type)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          ),

          const SizedBox(height: 20),

          const Text(
            'Deities',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableDeities.map((deity) {
              final isSelected = _selectedDeities.contains(deity);
              return FilterChip(
                label: Text(deity),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedDeities.add(deity);
                    } else {
                      _selectedDeities.remove(deity);
                    }
                  });
                },
                selectedColor: TraditionalColors.lightSaffron,
                checkmarkColor: TraditionalColors.templeOrange,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Location & Contact',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          TextFormField(
            controller: _addressController,
            maxLines: 2,
            decoration: const InputDecoration(
              labelText: 'Address *',
              hintText: 'Complete temple address',
              prefixIcon: Icon(Icons.location_on),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Address is required';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _cityController,
                  decoration: const InputDecoration(
                    labelText: 'City *',
                    prefixIcon: Icon(Icons.location_city),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'City is required';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _stateController,
                  decoration: const InputDecoration(
                    labelText: 'State *',
                    prefixIcon: Icon(Icons.map),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'State is required';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _pincodeController,
            keyboardType: TextInputType.number,
            decoration: const InputDecoration(
              labelText: 'Pincode *',
              prefixIcon: Icon(Icons.pin_drop),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Pincode is required';
              }
              if (value.length != 6) {
                return 'Please enter a valid 6-digit pincode';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _phoneController,
            keyboardType: TextInputType.phone,
            decoration: const InputDecoration(
              labelText: 'Phone Number *',
              prefixIcon: Icon(Icons.phone),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Phone number is required';
              }
              if (value.length != 10) {
                return 'Please enter a valid 10-digit phone number';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          EmailVerificationWidget(
            emailController: _emailController,
            onVerificationChanged: (isVerified) {
              setState(() {
                _isEmailVerified = isVerified;
              });
            },
            primaryColor: TraditionalColors.saffron,
          ),

          const SizedBox(height: 20),

          const Text(
            'Facilities',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableFacilities.map((facility) {
              final isSelected = _selectedFacilities.contains(facility);
              return FilterChip(
                label: Text(facility),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedFacilities.add(facility);
                    } else {
                      _selectedFacilities.remove(facility);
                    }
                  });
                },
                selectedColor: TraditionalColors.lightSaffron,
                checkmarkColor: TraditionalColors.templeOrange,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountTab() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Manager Account',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),

          TextFormField(
            controller: _managerNameController,
            decoration: const InputDecoration(
              labelText: 'Manager Name *',
              hintText: 'Temple manager full name',
              prefixIcon: Icon(Icons.person),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Manager name is required';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _managerEmailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(
              labelText: 'Manager Email *',
              hintText: 'Login email address',
              prefixIcon: Icon(Icons.email),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Email is required';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Please enter a valid email';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _passwordController,
            obscureText: _obscurePassword,
            decoration: InputDecoration(
              labelText: 'Password *',
              hintText: 'Create a strong password',
              prefixIcon: const Icon(Icons.lock),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscurePassword ? Icons.visibility_off : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _obscurePassword = !_obscurePassword;
                  });
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Password is required';
              }
              if (value.length < 8) {
                return 'Password must be at least 8 characters';
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          TextFormField(
            controller: _confirmPasswordController,
            obscureText: _obscureConfirmPassword,
            decoration: InputDecoration(
              labelText: 'Confirm Password *',
              hintText: 'Re-enter your password',
              prefixIcon: const Icon(Icons.lock_outline),
              suffixIcon: IconButton(
                icon: Icon(
                  _obscureConfirmPassword
                      ? Icons.visibility_off
                      : Icons.visibility,
                ),
                onPressed: () {
                  setState(() {
                    _obscureConfirmPassword = !_obscureConfirmPassword;
                  });
                },
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please confirm your password';
              }
              if (value != _passwordController.text) {
                return 'Passwords do not match';
              }
              return null;
            },
          ),

          const SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    const Text(
                      'Registration Process',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                const Text(
                  '• Your temple registration will be reviewed by our admin team\n'
                  '• You will receive an email notification once approved\n'
                  '• The review process typically takes 2-3 business days\n'
                  '• Ensure all information is accurate and complete',
                  style: TextStyle(fontSize: 14, height: 1.5),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          if (_tabController.index > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: () {
                  _tabController.animateTo(_tabController.index - 1);
                },
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Colors.white),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Previous'),
              ),
            ),
          if (_tabController.index > 0) const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _handleNext,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: TraditionalColors.templeOrange,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(
                      _tabController.index == 2 ? 'Register Temple' : 'Next',
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNext() {
    if (_tabController.index < 2) {
      if (_validateCurrentTab()) {
        _tabController.animateTo(_tabController.index + 1);
      }
    } else {
      _handleRegistration();
    }
  }

  bool _validateCurrentTab() {
    switch (_tabController.index) {
      case 0:
        return _templeNameController.text.isNotEmpty &&
            _descriptionController.text.isNotEmpty &&
            _selectedDeities.isNotEmpty;
      case 1:
        return _addressController.text.isNotEmpty &&
            _cityController.text.isNotEmpty &&
            _stateController.text.isNotEmpty &&
            _pincodeController.text.isNotEmpty &&
            _phoneController.text.isNotEmpty;
      case 2:
        return _formKey.currentState?.validate() ?? false;
      default:
        return true;
    }
  }

  Future<void> _handleRegistration() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_isEmailVerified) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please verify your email address first'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ref
          .read(templeAuthProvider.notifier)
          .registerTemple(
            templeName: _templeNameController.text.trim(),
            description: _descriptionController.text.trim(),
            type: _selectedType,
            address: _addressController.text.trim(),
            city: _cityController.text.trim(),
            stateName: _stateController.text.trim(),
            pincode: _pincodeController.text.trim(),
            phone: _phoneController.text.trim(),
            email: _emailController.text.trim(),
            deities: _selectedDeities,
            facilities: _selectedFacilities,
            managerName: _managerNameController.text.trim(),
            managerEmail: _managerEmailController.text.trim(),
            password: _passwordController.text,
          );

      if (success && mounted) {
        context.go('/temples/pending-approval');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Registration failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getTypeLabel(TempleType type) {
    switch (type) {
      case TempleType.ancient:
        return 'Ancient Temple';
      case TempleType.modern:
        return 'Modern Temple';
      case TempleType.heritage:
        return 'Heritage Temple';
      case TempleType.pilgrimage:
        return 'Pilgrimage Site';
      case TempleType.local:
        return 'Local Temple';
    }
  }
}
