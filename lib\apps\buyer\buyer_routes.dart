import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../shared/models/product/product_model.dart';
import '../../shared/models/review.dart';
import '../../shared/screens/language_selection_screen.dart';
import '../../shared/services/app_initialization_service.dart';
import 'widgets/navigation/buyer_shell.dart';
import 'screens/auth/login_screen.dart';
import 'screens/auth/register_screen.dart';
import 'screens/auth/forgot_password_screen.dart';


import 'screens/home/<USER>';
import 'screens/shopping/shopping_list_screen.dart' as shopping;
import 'screens/profile/profile_screen.dart';
import 'screens/media/media_screen.dart';
import 'screens/help/help_screen.dart';
import 'screens/chat/chat_screen.dart';
import 'utils/navigation_observer.dart';
import 'screens/cart/cart_screen.dart';
import 'screens/review/review_screen.dart' as review_screen;
import 'screens/review/create_review_screen.dart' as review_screen;
import 'screens/review/edit_review_screen.dart' as review_screen;
import 'screens/review/review_history_screen.dart' as review_screen;
import 'screens/notification/notification_screen.dart' as notification;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/shared/core/auth/bloc/auth_bloc.dart';
import 'screens/booking/booking_screen.dart' as booking_screen;
import 'screens/booking/booking_details_screen.dart' as booking_details;
import 'services/buyer_agreement_service.dart';
import 'screens/agreement/agreement_screen.dart';
import 'screens/healthcare/healthcare_screen.dart';
import 'screens/healthcare/doctors/doctors_screen.dart';
import 'screens/healthcare/appointments/doctor_appointment_screen.dart';
import 'screens/healthcare/appointments/doctor_appointments_screen.dart';
import 'screens/healthcare/appointments/appointment_details_screen.dart';
import 'screens/profile/edit_profile_screen.dart';
import 'screens/security/security_screen.dart';
import 'screens/settings/user_settings_screen.dart';
import 'screens/settings/theme_settings_screen.dart';
import 'screens/settings/language_settings_screen.dart';
import 'screens/settings/notification_settings_screen.dart';
import 'screens/settings/payment_settings_screen.dart';
import 'screens/settings/about_screen.dart';
import 'screens/settings/voice_assistant_settings_screen.dart';
import 'screens/settings/ai_assistant_settings_screen.dart';
import 'screens/settings/ai_intelligence_settings_screen.dart';
import 'screens/settings/ai_safety_settings_screen.dart';
import 'screens/settings/privacy_settings_screen.dart';
import 'screens/order/orders_screen.dart';
import 'screens/wishlist/wishlist_screen.dart';
import 'screens/address/address_list_screen.dart';
import 'screens/address/add_address_screen.dart';
import 'screens/address/edit_address_screen.dart';
import 'screens/category/category_screen.dart';
import 'screens/category/category_products_screen.dart';
import 'screens/booking/priest_booking_screen.dart';
import 'screens/booking/technician_booking_screen.dart';
import 'screens/events/events_screen.dart';
import 'screens/events/event_suppliers_screen.dart';
import 'screens/events/ai_event_assistant_screen.dart';
import 'screens/temple/temples_screen.dart';
import 'screens/temple/temple_details_screen.dart';
import 'screens/temple/temple_booking_screen.dart';
import 'screens/priest/priest_list_screen.dart';
import 'screens/technician/technician_list_screen.dart';
import 'screens/calendar/calendar_screen.dart';
import 'screens/calendar/calendar_event_details_screen.dart';
import 'screens/calendar/calendar_settings_screen.dart';
import 'screens/alarm/alarm_screen.dart';
import 'screens/alarm/alarm_settings_screen.dart';
import 'screens/product/search_screen.dart';
import 'screens/product/product_details_screen.dart' as product_details;
import 'screens/product/checkout_screen.dart';
import 'screens/payment/add_card_screen.dart';
import 'screens/payment/payment_screen.dart';
import 'screens/payment/phonepe_payment_screen.dart';
import 'screens/payment/phonepe_full_payment_screen.dart';
import 'screens/payment/payment_success_screen.dart';
import 'screens/seller/qr_scanner_screen.dart';
import 'screens/seller/favorite_sellers_screen.dart';
import 'screens/order/order_details_screen.dart';
import 'screens/order/order_tracking_screen.dart';
import 'screens/order/live_order_tracking_screen.dart';
import 'screens/ride/ride_tracking_screen.dart';
import 'screens/ride/ride_diagnostics_screen.dart';
import 'screens/friend_tracking_list_screen.dart';
import 'screens/friend_tracking_screen.dart';

class BuyerRoutes {
  static const String initialLocation = '/buyer/language-selection';

  // Auth Routes
  static const String login = '/buyer/login';
  static const String register = '/buyer/register';
  static const String forgotPassword = '/buyer/forgot-password';
  static const String agreement = '/buyer/agreement';



  // Main Routes
  static const String home = '/buyer/home';
  static const String profile = '/buyer/profile';
  static const String settings = '/buyer/settings';
  static const String shoppingList = '/buyer/shopping-list';
  static const String cart = '/buyer/cart';
  static const String checkout = '/buyer/checkout';
  static const String orders = '/buyer/orders';
  static const String orderDetails = '/buyer/orders/:id';
  static const String orderTracking = '/buyer/orders/:id/tracking';
  static const String liveOrderTracking = '/buyer/orders/:id/live-tracking';

  // Product Routes
  static const String productDetails = '/buyer/product/:id';
  static const String search = '/buyer/search';
  static const String categories = '/buyer/categories';
  static const String categoryProducts = '/buyer/categories/:id/products';

  // Shopping List Routes
  static const String listSubmission = '/buyer/list-submissions/:submissionId';
  static const String listSubmissionPayment =
      '/buyer/list-submissions/:submissionId/payment';
  static const String listSubmissionConvert =
      '/buyer/list-submissions/:submissionId/convert';
  static const String sellerSelection = '/buyer/sellers';

  // Payment Routes
  static const String addCard = '/buyer/payment/add-card';
  static const String paymentMethod = '/buyer/payment/method';
  static const String paymentProcessing = '/buyer/payment/processing';
  static const String paymentStatus = '/buyer/payment/status';
  static const String phonePePayment = '/buyer/payment/phonepe';
  static const String phonePeFullPayment = '/buyer/payment/phonepe-full';
  static const String paymentSuccess = '/buyer/payment/success';

  // Booking Routes
  static const String priestBooking = '/buyer/booking/priest';
  static const String technicianBooking = '/buyer/booking/technician';
  static const String priestList = '/buyer/priests';
  static const String technicianList = '/buyer/technicians';
  static const String booking = '/buyer/booking';
  static const String bookingDetails = '/buyer/booking/:id';

  // Healthcare Routes
  static const String healthcare = '/buyer/healthcare';
  static const String healthcareAppointments = '/buyer/healthcare/appointments';
  static const String healthcareDoctors = '/buyer/healthcare/doctors';
  static const String bookAppointment =
      '/buyer/healthcare/book-appointment/:id';
  static const String appointmentDetails = '/buyer/healthcare/appointment/:id';

  // Media Routes
  static const String media = '/buyer/media';

  // Chat Routes
  static const String chat = '/buyer/chat';

  // Help Routes
  static const String help = '/buyer/help';

  // Calendar Routes
  static const String calendar = '/buyer/calendar';
  static const String calendarSettings = '/buyer/calendar/settings';
  static const String calendarEventDetails = '/buyer/calendar/event/:id';

  // Events Routes
  static const String events = '/buyer/events';
  static const String eventServices = '/buyer/events/services/:category';
  static const String eventSuppliers = '/buyer/events/suppliers/:category';
  static const String eventSupplierDetails = '/buyer/events/supplier/:id';
  static const String eventBooking = '/buyer/events/booking/:supplierId';
  static const String aiShoppingAssistant =
      '/buyer/events/ai-shopping-assistant';

  // Temple Routes
  static const String temples = '/buyer/temples';
  static const String templeDetails = '/buyer/temples/:id';
  static const String templeBooking = '/buyer/temples/:id/booking';
  static const String templeBookingDetails = '/buyer/temple-bookings/:id';
  static const String nearbyTemples = '/buyer/temples/nearby';
  static const String templeSearch = '/buyer/temples/search';

  // Alarm Routes
  static const String alarm = '/buyer/alarm';
  static const String alarmSettings = '/buyer/alarm/settings';

  // Ride Routes
  static const String rideBooking = '/buyer/ride/booking';
  static const String rideTracking = '/buyer/ride/:id';
  static const String rideDiagnostics = '/buyer/ride/diagnostics';

  // Friend Tracking Routes
  static const String friendTracking = '/buyer/friend-tracking';
  static const String friendTrackingDetails = '/buyer/friend-tracking/:rideId';

  // Review Routes
  static const String review = '/buyer/review/:id';
  static const String createReview = '/buyer/review/create';
  static const String editReview = '/buyer/review/edit';
  static const String reviewHistory = '/buyer/review/history';

  // Notification Routes
  static const String notifications = '/buyer/notifications';

  // Wishlist Routes
  static const String wishlist = '/buyer/wishlist';

  // Seller Routes
  static const String qrScanner = '/buyer/sellers/qr-scanner';
  static const String favoriteSellers = '/buyer/sellers/favorites';

  // Address Routes
  static const String addresses = '/buyer/addresses';
  static const String addAddress = '/buyer/addresses/add';
  static const String editAddress = '/buyer/addresses/:id/edit';

  // Language Settings Routes
  static const String languageSettings = '/buyer/settings/language';
  static const String languageSelection = '/buyer/language-selection';

  // Notification Settings Routes
  static const String notificationSettings = '/buyer/settings/notifications';

  // Payment Settings Routes
  static const String paymentSettings = '/buyer/settings/payment';

  // Voice Assistant Settings Route
  static const String voiceAssistantSettings =
      '/buyer/settings/voice-assistant';

  // AI Assistant Settings Route
  static const String aiAssistantSettings = '/buyer/settings/ai-assistant';

  // AI Intelligence Settings Route
  static const String aiIntelligenceSettings =
      '/buyer/settings/ai-intelligence';

  // AI Safety Settings Route
  static const String aiSafetySettings = '/buyer/settings/ai-safety';

  // Privacy Settings Route
  static const String privacySettings = '/buyer/settings/privacy';

  // Security Settings Route
  static const String securitySettings = '/buyer/settings/security';

  // About Screen Route
  static const String about = '/buyer/settings/about';

  // Navigation methods
  static void navigateToLogin(BuildContext context) => context.go(login);
  static void navigateToRegister(BuildContext context) => context.go(register);
  static void navigateToForgotPassword(BuildContext context) =>
      context.go(forgotPassword);
  static void navigateToHome(BuildContext context) => context.go(home);
  static void navigateToProfile(BuildContext context) => context.go(profile);
  static void navigateToSettings(BuildContext context) => context.go(settings);
  static void navigateToShoppingList(BuildContext context) =>
      context.go(shoppingList);
  static void navigateToCart(BuildContext context) => context.go(cart);
  static void navigateToCheckout(BuildContext context) => context.go(checkout);
  static void navigateToOrders(BuildContext context) {
    // Just navigate to orders screen - the screen will refresh itself in initState
    context.go(orders);
  }

  static void navigateToOrderDetails(BuildContext context, String orderId) {
    final path = orderDetails.replaceAll(':id', orderId);
    debugPrint('Navigating to order details path: $path');
    context.go(path);
  }

  static void navigateToOrderTracking(BuildContext context, String orderId) =>
      context.go(orderTracking.replaceAll(':id', orderId));

  static void navigateToLiveOrderTracking(
    BuildContext context,
    String orderId,
  ) => context.go(liveOrderTracking.replaceAll(':id', orderId));
  static void navigateToProductDetails(
    BuildContext context,
    String productId,
  ) => context.go(productDetails.replaceAll(':id', productId));
  static void navigateToProductDetailsWithData(
    BuildContext context,
    ProductModel product,
  ) => context.go(productDetails.replaceAll(':id', product.id), extra: product);
  static void navigateToCalendar(BuildContext context) => context.go(calendar);
  static void navigateToAlarm(BuildContext context) => context.go(alarm);
  static void navigateToAlarmSettings(BuildContext context) =>
      context.go(alarmSettings);
  static void navigateToRideBooking(BuildContext context) =>
      context.go(rideBooking);
  static void navigateToRideTracking(BuildContext context, String rideId) =>
      context.go(rideTracking.replaceAll(':id', rideId));
  static void navigateToRideDiagnostics(BuildContext context) =>
      context.go(rideDiagnostics);
  static void navigateToMedia(BuildContext context) => context.go(media);
  static void navigateToChat(BuildContext context) => context.go(chat);
  static void navigateToHelp(BuildContext context) => context.go(help);
  static void navigateToPriestBooking(BuildContext context) =>
      context.go(priestBooking);
  static void navigateToTechnicianBooking(BuildContext context) =>
      context.go(technicianBooking);
  static void navigateToPriestList(BuildContext context) =>
      context.go(priestList);
  static void navigateToTechnicianList(BuildContext context) =>
      context.go(technicianList);

  // Healthcare navigation methods
  static void navigateToHealthcare(BuildContext context) =>
      context.go(healthcare);
  static void navigateToHealthcareAppointments(BuildContext context) =>
      context.go(healthcareAppointments);
  static void navigateToHealthcareDoctors(BuildContext context) =>
      context.go(healthcareDoctors);
  static void navigateToReview(BuildContext context, String productId) =>
      context.go(review.replaceAll(':id', productId));
  static void navigateToCreateReview(
    BuildContext context,
    String productId,
    String orderId,
  ) => context.go(
    createReview,
    extra: {'productId': productId, 'orderId': orderId},
  );
  static void navigateToEditReview(BuildContext context, Review review) =>
      context.go(editReview, extra: review);
  static void navigateToReviewHistory(BuildContext context) =>
      context.go(reviewHistory);
  static void navigateToAddCard(BuildContext context) => context.go(addCard);
  static void navigateToPaymentMethod(
    BuildContext context,
    double amount,
    String orderId,
  ) => context.go(paymentMethod, extra: {'amount': amount, 'orderId': orderId});
  static void navigateToNotifications(BuildContext context) =>
      context.go(notifications);
  static void navigateToSearch(BuildContext context) => context.go(search);
  static void navigateToCategories(BuildContext context) =>
      context.go(categories);
  static void navigateToCategoryProducts(
    BuildContext context,
    String categoryId,
  ) => context.go(categoryProducts.replaceAll(':id', categoryId));
  static void navigateToWishlist(BuildContext context) => context.go(wishlist);
  static void navigateToQRScanner(BuildContext context) =>
      context.go(qrScanner);
  static void navigateToFavoriteSellers(BuildContext context) =>
      context.go(favoriteSellers);
  static void navigateToAddresses(BuildContext context) =>
      context.go(addresses);
  static void navigateToAddAddress(BuildContext context) =>
      context.go(addAddress);
  static void navigateToEditAddress(BuildContext context, String addressId) =>
      context.go(editAddress.replaceAll(':id', addressId));
  static void navigateToLanguageSettings(BuildContext context) =>
      context.go(languageSettings);
  static void navigateToNotificationSettings(BuildContext context) =>
      context.go(notificationSettings);
  static void navigateToPaymentSettings(BuildContext context) =>
      context.go(paymentSettings);
  static void navigateToVoiceAssistantSettings(BuildContext context) =>
      context.go(voiceAssistantSettings);
  static void navigateToAIAssistantSettings(BuildContext context) =>
      context.go(aiAssistantSettings);
  static void navigateToAIIntelligenceSettings(BuildContext context) =>
      context.go(aiIntelligenceSettings);
  static void navigateToAISafetySettings(BuildContext context) =>
      context.go(aiSafetySettings);
  static void navigateToPrivacySettings(BuildContext context) =>
      context.go(privacySettings);
  static void navigateToSecuritySettings(BuildContext context) =>
      context.go(securitySettings);
  static void navigateToAbout(BuildContext context) => context.go(about);

  // Get routes for centralized router
  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: languageSelection,
        builder: (context, state) => LanguageSelectionScreen(
          onComplete: (confirmed) {
            debugPrint(
              'LanguageSelectionScreen onComplete called with: $confirmed',
            );
            if (confirmed) {
              debugPrint('Navigating to agreement screen: $agreement');
              context.go(agreement);
            }
          },
        ),
      ),
      ShellRoute(
        builder: (context, state, child) {
          return BuyerShell(child: child);
        },
        routes: [
          // Main tab routes
          GoRoute(
            path: home,
            builder: (context, state) => const ModernHomeScreen(),
          ),
          GoRoute(
            path: priestBooking,
            builder: (context, state) => const PriestBookingScreen(),
          ),
          GoRoute(
            path: shoppingList,
            builder: (context, state) => const shopping.ShoppingListScreen(),
          ),
          GoRoute(
            path: alarm,
            builder: (context, state) => const AlarmScreen(),
          ),
          GoRoute(
            path: calendar,
            builder: (context, state) => const CalendarScreen(),
          ),
          GoRoute(
            path: technicianBooking,
            builder: (context, state) => const TechnicianBookingScreen(),
          ),
          GoRoute(
            path: events,
            builder: (context, state) => const EventsScreen(),
          ),
          GoRoute(
            path: temples,
            builder: (context, state) => const TemplesScreen(),
          ),
          GoRoute(
            path: templeDetails,
            builder: (context, state) {
              final templeId = state.pathParameters['id']!;
              return TempleDetailsScreen(templeId: templeId);
            },
          ),
          GoRoute(
            path: templeBooking,
            builder: (context, state) {
              final templeId = state.pathParameters['id']!;
              return TempleBookingScreen(templeId: templeId);
            },
          ),
          GoRoute(
            path: eventSuppliers,
            builder: (context, state) {
              final category = state.pathParameters['category']!;
              return EventSuppliersScreen(category: category);
            },
          ),
          GoRoute(
            path: aiShoppingAssistant,
            builder: (context, state) => const AIEventAssistantScreen(),
          ),
          GoRoute(
            path: priestList,
            builder: (context, state) => const PriestListScreen(),
          ),
          GoRoute(
            path: technicianList,
            builder: (context, state) => const TechnicianListScreen(),
          ),
          GoRoute(
            path: profile,
            builder: (context, state) => const ProfileScreen(),
          ),

          // Home tab related routes
          GoRoute(
            path: search,
            builder: (context, state) => const SearchScreen(),
          ),
          GoRoute(
            path: categories,
            builder: (context, state) => const CategoryScreen(),
          ),
          GoRoute(
            path: categoryProducts,
            builder: (context, state) {
              final categoryId = state.pathParameters['id']!;
              return CategoryProductsScreen(categoryId: categoryId);
            },
          ),
          // Product details route
          GoRoute(
            path: productDetails,
            builder: (context, state) {
              final productId = state.pathParameters['id']!;
              final product = state.extra as ProductModel?;

              // If we have the product data already, use it directly
              if (product != null) {
                return product_details.ProductDetailsScreen(product: product);
              }

              // Otherwise, we need to fetch the product data first
              // For now, we'll use a placeholder that will fetch the product
              return product_details.ProductDetailsScreen.fromId(
                productId: productId,
              );
            },
          ),
          GoRoute(
            path: wishlist,
            builder: (context, state) => const WishlistScreen(),
          ),
          GoRoute(path: cart, builder: (context, state) => const CartScreen()),

          // Profile tab related routes
          GoRoute(
            path: '/buyer/profile/edit',
            builder: (context, state) => const EditProfileScreen(),
          ),
          GoRoute(
            path: '/buyer/security',
            builder: (context, state) => const SecurityScreen(),
          ),
          GoRoute(
            path: orders,
            builder: (context, state) => const OrdersScreen(),
          ),
          GoRoute(
            path: orderDetails,
            builder: (context, state) {
              final orderId = state.pathParameters['id']!;
              return OrderDetailsScreen(orderId: orderId);
            },
          ),
          GoRoute(
            path: addresses,
            builder: (context, state) => const AddressListScreen(),
          ),
          GoRoute(
            path: addAddress,
            builder: (context, state) => const AddAddressScreen(),
          ),
          GoRoute(
            path: editAddress,
            builder: (context, state) {
              final addressId = state.pathParameters['id']!;
              return EditAddressScreen(addressId: addressId);
            },
          ),
        ],
      ),
      // Standalone routes (not part of bottom navigation)
      GoRoute(
        path: agreement,
        builder: (context, state) => const AgreementScreen(),
      ),
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: forgotPassword,
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),


      // Calendar Routes
      GoRoute(
        path: calendarSettings,
        name: 'calendar_settings',
        builder: (context, state) => const CalendarSettingsScreen(),
      ),
      GoRoute(
        path: calendarEventDetails,
        name: 'calendar_event_details',
        builder: (context, state) {
          final eventId = state.pathParameters['id']!;
          return CalendarEventDetailsScreen.fromId(eventId: eventId);
        },
      ),
      // Alarm Settings Route
      GoRoute(
        path: alarmSettings,
        builder: (context, state) => const AlarmSettingsScreen(),
      ),
      // Search Routes - moved to ShellRoute
      // Ride Routes - now handled by the bottom drawer
      // Keep this route for backward compatibility, but it will redirect to home
      GoRoute(path: rideBooking, redirect: (context, state) => home),
      GoRoute(
        path: rideDiagnostics,
        builder: (context, state) => const RideDiagnosticsScreen(),
      ),
      // Friend Tracking Routes
      GoRoute(
        path: friendTracking,
        builder: (context, state) => const FriendTrackingListScreen(),
      ),
      GoRoute(
        path: friendTrackingDetails,
        builder: (context, state) {
          final rideId = state.pathParameters['rideId']!;
          final userName = state.uri.queryParameters['userName'] ?? 'User';
          final friendPhone = state.uri.queryParameters['friendPhone'] ?? '';
          return FriendTrackingScreen(
            rideId: rideId,
            friendPhone: friendPhone,
            userName: userName,
          );
        },
      ),
      GoRoute(
        path: rideTracking,
        builder: (context, state) {
          final rideId = state.pathParameters['id']!;
          debugPrint('Opening ride tracking screen with ID: $rideId');
          return RideTrackingScreen(rideId: rideId);
        },
      ),
      // Media Routes
      GoRoute(path: media, builder: (context, state) => const MediaScreen()),
      // Notification Routes
      GoRoute(
        path: notifications,
        builder: (context, state) => const notification.NotificationScreen(),
      ),
      // Chat Routes
      GoRoute(path: chat, builder: (context, state) => const ChatScreen()),
      // Help Routes
      GoRoute(path: help, builder: (context, state) => const HelpScreen()),
      // Booking Routes
      GoRoute(
        path: booking,
        builder: (context, state) => const booking_screen.BookingScreen(),
      ),
      GoRoute(
        path: bookingDetails,
        builder: (context, state) => booking_details.BookingDetailsScreen(
          bookingId: state.pathParameters['id']!,
        ),
      ),
      // Review Routes
      GoRoute(
        path: review,
        builder: (context, state) {
          return review_screen.ReviewScreen(
            productId: state.pathParameters['id']!,
          );
        },
      ),
      GoRoute(
        path: createReview,
        builder: (context, state) {
          final extra = state.extra as Map<String, String>?;
          return review_screen.CreateReviewScreen(
            productId: extra?['productId'] ?? '',
            orderId: extra?['orderId'] ?? '',
          );
        },
      ),
      GoRoute(
        path: editReview,
        builder: (context, state) {
          final review = state.extra as Review?;
          if (review == null) {
            return const Scaffold(
              body: Center(child: Text('Review not found')),
            );
          }
          return review_screen.EditReviewScreen(review: review);
        },
      ),
      GoRoute(
        path: reviewHistory,
        builder: (context, state) => const review_screen.ReviewHistoryScreen(),
      ),
      // Profile Routes
      GoRoute(
        path: '/buyer/profile/edit',
        builder: (context, state) => const EditProfileScreen(),
      ),
      // Security Route
      GoRoute(
        path: '/buyer/security',
        builder: (context, state) => const SecurityScreen(),
      ),
      // Settings Routes
      GoRoute(path: settings, builder: (context, state) => SettingsScreen()),
      GoRoute(
        path: '/buyer/settings/theme',
        builder: (context, state) => const ThemeSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/language',
        builder: (context, state) => const LanguageSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/notifications',
        builder: (context, state) => const NotificationSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/payment',
        builder: (context, state) => const PaymentSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/voice-assistant',
        builder: (context, state) => const VoiceAssistantSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/ai-assistant',
        builder: (context, state) => const AIAssistantSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/ai-intelligence',
        builder: (context, state) => const AIIntelligenceSettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/ai-safety',
        builder: (context, state) => const AISafetySettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/privacy',
        builder: (context, state) => const PrivacySettingsScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/security',
        builder: (context, state) => const SecurityScreen(),
      ),
      GoRoute(
        path: '/buyer/settings/about',
        builder: (context, state) => const AboutScreen(),
      ),

      // Orders Routes
      GoRoute(
        path: orderTracking,
        builder: (context, state) {
          final orderId = state.pathParameters['id']!;
          return OrderTrackingScreen(orderId: orderId);
        },
      ),
      GoRoute(
        path: liveOrderTracking,
        builder: (context, state) {
          final orderId = state.pathParameters['id']!;
          return LiveOrderTrackingScreen(orderId: orderId);
        },
      ),
      // Checkout Route
      GoRoute(
        path: checkout,
        builder: (context, state) => const CheckoutScreen(),
      ),
      // Payment Routes
      GoRoute(
        path: addCard,
        builder: (context, state) => const AddCardScreen(),
      ),
      GoRoute(
        path: paymentMethod,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final amount = extra?['amount'] as double? ?? 0.0;
          final orderId = extra?['orderId'] as String? ?? '';
          return ModernPaymentScreen(amount: amount, orderId: orderId);
        },
      ),
      GoRoute(
        path: phonePePayment,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final amount = extra?['amount'] as double? ?? 0.0;
          final orderId = extra?['orderId'] as String? ?? '';
          final customerName = extra?['customerName'] as String? ?? '';
          final customerPhone = extra?['customerPhone'] as String? ?? '';
          final customerEmail = extra?['customerEmail'] as String? ?? '';
          final description = extra?['description'] as String?;

          return PhonePePaymentScreen(
            orderId: orderId,
            amount: amount,
            customerName: customerName,
            customerPhone: customerPhone,
            customerEmail: customerEmail,
            description: description,
          );
        },
      ),
      GoRoute(
        path: phonePeFullPayment,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final amount = extra?['amount'] as double? ?? 0.0;
          final orderId = extra?['orderId'] as String? ?? '';
          final customerName = extra?['customerName'] as String? ?? '';
          final customerPhone = extra?['customerPhone'] as String? ?? '';
          final customerEmail = extra?['customerEmail'] as String? ?? '';
          final description = extra?['description'] as String?;

          return PhonePeFullPaymentScreen(
            orderId: orderId,
            amount: amount,
            customerName: customerName,
            customerPhone: customerPhone,
            customerEmail: customerEmail,
            description: description,
          );
        },
      ),
      GoRoute(
        path: paymentSuccess,
        builder: (context, state) {
          final extra = state.extra as Map<String, dynamic>?;
          final amount = extra?['amount'] as double? ?? 0.0;
          final orderId = extra?['orderId'] as String? ?? '';
          final transactionId = extra?['transactionId'] as String?;

          return PaymentSuccessScreen(
            orderId: orderId,
            amount: amount,
            transactionId: transactionId,
          );
        },
      ),
      // QR Scanner Route
      GoRoute(
        path: qrScanner,
        builder: (context, state) => const QRScannerScreen(),
      ),
      // Favorite Sellers Route
      GoRoute(
        path: favoriteSellers,
        builder: (context, state) => const FavoriteSellersScreen(),
      ),

      // Healthcare Routes
      GoRoute(
        path: healthcare,
        builder: (context, state) => const HealthcareScreen(),
      ),
      GoRoute(
        path: healthcareAppointments,
        builder: (context, state) => const DoctorAppointmentsScreen(),
      ),
      GoRoute(
        path: healthcareDoctors,
        builder: (context, state) => const DoctorsScreen(),
      ),
      GoRoute(
        path: bookAppointment,
        builder: (context, state) {
          final doctorId = state.pathParameters['id']!;
          return BookAppointmentScreen(doctorId: doctorId);
        },
      ),
      GoRoute(
        path: appointmentDetails,
        builder: (context, state) {
          final appointmentId = state.pathParameters['id']!;
          return DoctorAppointmentScreen(appointmentId: appointmentId);
        },
      ),
      // Wishlist Route - moved to ShellRoute
      // Category Routes - moved to ShellRoute
      // Booking Routes - moved to ShellRoute
      // Address Routes - moved to ShellRoute
    ];
  }

  static final GoRouter router = GoRouter(
    initialLocation: initialLocation,
    // Enable history state restoration for proper back button handling
    restorationScopeId: 'buyer_app',
    // Enable navigation history
    navigatorKey: GlobalKey<NavigatorState>(),
    // Add observers to debug navigation
    observers: [NavigationObserver()],
    // Reduce redirects to avoid breaking back button
    redirect: (context, state) async {
      debugPrint('Redirect called for path: ${state.matchedLocation}');

      // Define route checks
      final isLanguageSelectionRoute =
          state.matchedLocation == languageSelection;
      final isLoginRoute = state.matchedLocation == login;
      final isRegisterRoute = state.matchedLocation == register;
      final isForgotPasswordRoute = state.matchedLocation == forgotPassword;
      final isAgreementRoute = state.matchedLocation == agreement;
      final isAuthRoute =
          isLoginRoute ||
          isRegisterRoute ||
          isForgotPasswordRoute ||
          isAgreementRoute; // Include agreement route to avoid redirect loops

      // Get auth state at the beginning to avoid async gap issues
      final authState = context.read<AuthBloc>().state;
      final isAuthenticated = authState.maybeWhen(
        authenticated: (_) => true,
        orElse: () => false,
      );
      debugPrint('Is authenticated: $isAuthenticated');

      // Don't redirect if we're on the language selection screen
      if (isLanguageSelectionRoute) {
        debugPrint('On language selection screen, no redirect');
        return null;
      }

      // Check if language has been selected
      final languageSelected =
          await AppInitializationService.hasLanguageBeenSelected();
      if (!languageSelected && !isLanguageSelectionRoute) {
        debugPrint('Language not selected, redirecting to language selection');
        return languageSelection;
      }

      // Check if confirmation is completed
      final hasConfirmed =
          await AppInitializationService.hasConfirmationBeenGiven();
      debugPrint('Has confirmed: $hasConfirmed');

      // If confirmation is not completed, redirect to language selection
      if (!hasConfirmed && !isLanguageSelectionRoute) {
        debugPrint('Not confirmed, redirecting to language selection');
        return languageSelection;
      }

      // Check if the user has accepted the legal agreement
      // We only check this for non-auth routes to avoid redirect loops
      if (!isAgreementRoute && !isAuthRoute) {
        final hasAcceptedAgreement =
            await BuyerAgreementService.hasAcceptedAgreement();
        debugPrint('Has accepted agreement: $hasAcceptedAgreement');

        // If the user hasn't accepted the agreement and is not on the agreement screen,
        // redirect to the agreement screen
        if (!hasAcceptedAgreement) {
          debugPrint('Not accepted agreement, redirecting to agreement screen');
          return agreement;
        }
      }

      // Store the current path to avoid unnecessary redirects
      final currentPath = state.matchedLocation;

      // Skip redirect for certain paths to prevent unnecessary navigation
      final skipRedirectPaths = [
        '/buyer/home',
        '/buyer/profile',
        '/buyer/settings',
        '/buyer/shopping-list',
        '/buyer/cart',
        '/buyer/orders',
        '/buyer/product',
        '/buyer/categories',
        '/buyer/search',
        '/buyer/booking',
        '/buyer/calendar',
        '/buyer/alarm',
        '/buyer/healthcare',
        '/buyer/media',
        '/buyer/notifications',
        '/buyer/chat',
        '/buyer/help',
        '/buyer/ride',
        '/buyer/review',
        '/buyer/addresses',
        '/buyer/payment',
        '/buyer/sellers',
      ];

      // If we're already on a valid path and authenticated, don't redirect
      final isOnValidPath = skipRedirectPaths.any(
        (path) => currentPath.startsWith(path),
      );

      // Handle authentication redirects - but be more conservative
      if (!isAuthenticated && !isAuthRoute && !isAgreementRoute) {
        debugPrint(
          'Not authenticated and not on auth route, redirecting to login',
        );
        return login;
      }

      if (isAuthenticated && isAuthRoute) {
        debugPrint('Authenticated and on auth route, redirecting to home');
        return home;
      }

      // If we're already on a valid path and authenticated, don't redirect
      if (isAuthenticated && isOnValidPath) {
        debugPrint(
          'Already on a valid path and authenticated, skipping redirect',
        );
        return null;
      }

      debugPrint('No redirect needed');
      return null;
    },
    routes: getRoutes(),
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('Error')),
      body: Center(
        child: Text(
          'Error: ${state.error}',
          style: Theme.of(context).textTheme.titleLarge,
        ),
      ),
    ),
  );
}
