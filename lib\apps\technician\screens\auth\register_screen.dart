import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/core/auth/models/register_request.dart';
import 'package:shivish/shared/models/user/user_model.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/apps/technician/application/technician_provider.dart';
import 'package:shivish/apps/technician/technician_routes.dart';
import 'package:shivish/shared/database/services/database_service.dart';
import 'package:shivish/shared/database/config/database_config.dart';
import 'package:shivish/shared/services/storage_service.dart';
import 'package:shivish/shared/widgets/auth/email_verification_widget.dart';

final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService(DatabaseConfig.fromEnvironment());
});

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _specializationController = TextEditingController();
  final _experienceController = TextEditingController();
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  bool _isEmailVerified = false;

  // Document related variables
  bool _isCheckingDocumentRequirements = true;
  bool _documentsRequired = false;
  File? _certificationDocument;
  File? _idProofDocument;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _checkDocumentRequirements();
  }

  Future<void> _checkDocumentRequirements() async {
    setState(() {
      _isCheckingDocumentRequirements = true;
    });

    try {
      // Try to fetch from hybrid database
      final databaseService = ref.read(databaseServiceProvider);
      final configData = await databaseService.find('system_config', 'registration');

      if (configData != null) {
        final bool docsRequired = configData['documents_required'] == true;
        debugPrint('Database documents_required: $docsRequired');

        setState(() {
          _documentsRequired = docsRequired;
          _isCheckingDocumentRequirements = false;
        });
      } else {
        debugPrint('Registration config document does not exist in database');
        setState(() {
          _documentsRequired = false;
          _isCheckingDocumentRequirements = false;
        });
      }
    } catch (e) {
      debugPrint('Error checking document requirements: $e');
      setState(() {
        _documentsRequired = false;
        _isCheckingDocumentRequirements = false;
      });
    }
  }

  // Pick document file
  Future<void> _pickDocument(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          if (type == 'certification') {
            _certificationDocument = file;
          } else if (type == 'id_proof') {
            _idProofDocument = file;
          }
        });
      }
    } catch (e) {
      debugPrint('Error picking $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Upload document to storage service
  Future<String?> _uploadDocument(File file, String type) async {
    try {
      final storageService = ref.read(storageServiceProvider);
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final filePath = 'technician_documents/${_emailController.text.trim()}/${type}_$fileName';

      final downloadUrl = await storageService.uploadFile(
        file: file,
        path: filePath,
      );

      return downloadUrl;
    } catch (e) {
      debugPrint('Error uploading $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _specializationController.dispose();
    _experienceController.dispose();
    super.dispose();
  }

  Future<void> _handleRegister() async {
    if (!_formKey.currentState!.validate()) return;

    // Check email verification first
    if (!_isEmailVerified) {
      setState(() {
        _errorMessage = 'Please verify your email address first';
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Only check for documents if documents are required
    if (_documentsRequired) {
      if (_certificationDocument == null) {
        setState(() {
          _errorMessage = 'Please upload your certification document';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_idProofDocument == null) {
        setState(() {
          _errorMessage = 'Please upload your ID proof document';
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    }

    // Get the current state
    final currentState = ref.read(technicianProvider);

    // Check if already loading
    if (currentState.isLoading) return;

    // Set loading state
    ref.read(technicianProvider.notifier).clearError();
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Create metadata map
      Map<String, dynamic> metadata = {
        'phone': _phoneController.text.trim(),
        'specializations': _specializationController.text.isEmpty
            ? []
            : _specializationController.text.split(',').map((e) => e.trim()).toList(),
        'serviceAreas': [],
        'experienceYears': _experienceController.text.isEmpty
            ? 0
            : int.tryParse(_experienceController.text) ?? 0,
        'description': 'Technician with ${_experienceController.text.isEmpty ? "0" : _experienceController.text} years of experience',
      };

      // Upload documents if required
      if (_documentsRequired) {

        String? certificationUrl;
        String? idProofUrl;

        // Upload documents
        certificationUrl = await _uploadDocument(_certificationDocument!, 'certification');
        idProofUrl = await _uploadDocument(_idProofDocument!, 'id_proof');

        // Check if all uploads were successful
        if (certificationUrl == null || idProofUrl == null) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'Failed to upload documents. Please try again.';
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(_errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }

        // Add document URLs to metadata
        metadata['certification_document'] = certificationUrl;
        metadata['id_proof_document'] = idProofUrl;
      }

      final request = RegisterRequest(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        displayName: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        role: UserRole.technician,
        metadata: metadata,
      );

      // Register the technician using the TechnicianNotifier
      await ref.read(technicianProvider.notifier).register(request);

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Registration successful! Your account is pending approval.'),
          backgroundColor: Colors.green,
          duration: Duration(seconds: 3),
        ),
      );

      // Wait a moment for the snackbar to be visible
      await Future.delayed(const Duration(milliseconds: 1000));

      if (!mounted) return;

      // Navigate to pending approval screen
      context.go(TechnicianRoutes.pendingApproval);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppToolbar.simple(
        title: 'Technician Registration',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Create Account',
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'Fill in your details to get started',
                style: Theme.of(context).textTheme.bodyLarge,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  prefixIcon: Icon(Icons.person_outline),
                ),
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              EmailVerificationWidget(
                emailController: _emailController,
                onVerificationChanged: (isVerified) {
                  setState(() {
                    _isEmailVerified = isVerified;
                  });
                },
                primaryColor: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  prefixIcon: Icon(Icons.phone_outlined),
                ),
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your phone number';
                  }
                  if (value.length < 10) {
                    return 'Please enter a valid phone number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _specializationController,
                decoration: const InputDecoration(
                  labelText: 'Specializations (comma separated)',
                  prefixIcon: Icon(Icons.build_outlined),
                  hintText: 'e.g. Plumbing, Electrical, Carpentry',
                ),
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _experienceController,
                decoration: const InputDecoration(
                  labelText: 'Years of Experience',
                  prefixIcon: Icon(Icons.work_outline),
                ),
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'Password',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                    ),
                    onPressed: () {
                      setState(() => _obscurePassword = !_obscurePassword);
                    },
                  ),
                ),
                obscureText: _obscurePassword,
                textInputAction: TextInputAction.next,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: InputDecoration(
                  labelText: 'Confirm Password',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscureConfirmPassword
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                    ),
                    onPressed: () {
                      setState(() =>
                          _obscureConfirmPassword = !_obscureConfirmPassword);
                    },
                  ),
                ),
                obscureText: _obscureConfirmPassword,
                textInputAction: TextInputAction.done,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please confirm your password';
                  }
                  if (value != _passwordController.text) {
                    return 'Passwords do not match';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),

              // Only show document upload section if documents are required
              if (_isCheckingDocumentRequirements)
                const Center(child: CircularProgressIndicator())
              else if (_documentsRequired) ...[
                const Divider(),
                const SizedBox(height: 16),
                const Text(
                  'Required Documents',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  'Please upload the following documents to complete your registration.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 16),

                // Certification Document
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Certification Document',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Upload your professional certification or qualification document.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _certificationDocument != null
                                    ? _certificationDocument!.path.split('/').last
                                    : 'No file selected',
                                style: TextStyle(
                                  color: _certificationDocument != null
                                      ? Colors.green
                                      : Colors.grey,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton.icon(
                              onPressed: () => _pickDocument('certification'),
                              icon: const Icon(Icons.upload_file),
                              label: const Text('Upload'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // ID Proof Document
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'ID Proof Document',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Upload a government-issued ID proof (Aadhar, PAN, Driving License, etc.).',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _idProofDocument != null
                                    ? _idProofDocument!.path.split('/').last
                                    : 'No file selected',
                                style: TextStyle(
                                  color: _idProofDocument != null
                                      ? Colors.green
                                      : Colors.grey,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const SizedBox(width: 16),
                            ElevatedButton.icon(
                              onPressed: () => _pickDocument('id_proof'),
                              icon: const Icon(Icons.upload_file),
                              label: const Text('Upload'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: _isLoading ? null : _handleRegister,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('Register'),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  context.pop();
                },
                child: const Text('Already have an account? Sign in'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
