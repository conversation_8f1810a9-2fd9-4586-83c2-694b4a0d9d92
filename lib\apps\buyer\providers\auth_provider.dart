import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../../shared/models/user/user_model.dart';
import '../../../shared/database/services/database_service.dart';
import '../../../shared/database/config/database_config.dart';


/// Auth state class
class AuthState {
  final bool isLoading;
  final User? user;
  final String? error;
  final bool isEmailVerified;
  final bool isBiometricAvailable;
  final bool isBiometricEnabled;

  const AuthState({
    this.isLoading = false,
    this.user,
    this.error,
    this.isEmailVerified = false,
    this.isBiometricAvailable = false,
    this.isBiometricEnabled = false,
  });

  AuthState copyWith({
    bool? isLoading,
    User? user,
    String? error,
    bool? isEmailVerified,
    bool? isBiometricAvailable,
    bool? isBiometricEnabled,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error ?? this.error,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isBiometricAvailable: isBiometricAvailable ?? this.isBiometricAvailable,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
    );
  }
}

/// Provider for the auth state
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

/// Notifier for managing auth state
class AuthNotifier extends StateNotifier<AuthState> {
  final _supabase = Supabase.instance.client;
  final _databaseService = DatabaseService(DatabaseConfig.fromEnvironment());
  final _googleSignIn = GoogleSignIn.instance;
  final _localAuth = LocalAuthentication();
  final _secureStorage = const FlutterSecureStorage();

  AuthNotifier() : super(const AuthState()) {
    _init();
  }

  void _init() {
    // Listen for auth state changes using Supabase
    _supabase.auth.onAuthStateChange.listen((data) {
      final user = data.session?.user;
      state = state.copyWith(
        user: user,
        isEmailVerified: user?.emailConfirmedAt != null,
      );

      // If user is null (signed out), try automatic re-login
      if (user == null) {
        _attemptAutomaticReLogin();
      }
    });

    // Check biometric availability
    _checkBiometricAvailability();

    // Try automatic re-login on startup
    _attemptAutomaticReLogin();
  }

  /// Attempt to automatically re-login using stored credentials
  Future<void> _attemptAutomaticReLogin() async {
    try {
      // Check if we already have a user
      if (_supabase.auth.currentUser != null) {
        debugPrint('User already signed in, no need for automatic re-login');
        return;
      }

      debugPrint('No current user found, checking for stored credentials');

      // Check for stored credentials in secure storage
      final storedEmail = await _secureStorage.read(key: 'email');
      final storedPassword = await _secureStorage.read(key: 'password');
      final storedRefreshToken =
          await _secureStorage.read(key: 'refresh_token');

      if (storedEmail != null && storedPassword != null) {
        debugPrint('Found stored credentials, attempting automatic re-login');

        // Don't store credentials again to avoid recursive calls
        await signInWithEmailAndPassword(
          email: storedEmail,
          password: storedPassword,
          storeCredentials: false,
        );

        debugPrint('Automatic re-login successful');
      } else if (storedRefreshToken != null) {
        debugPrint('Found refresh token, attempting to restore session');

        try {
          // This is a placeholder - Firebase doesn't directly expose refresh token usage
          // In a real implementation, you would use the refresh token to get a new ID token
          debugPrint(
              'Note: Refresh token functionality requires custom backend implementation');
        } catch (e) {
          debugPrint('Error using refresh token: $e');
        }
      } else {
        debugPrint('No stored credentials found for automatic re-login');
      }
    } catch (e) {
      debugPrint('Error during automatic re-login: $e');
      // Don't update state with error to avoid showing error to user
    }
  }

  Future<void> _checkBiometricAvailability() async {
    try {
      debugPrint('Checking biometric availability...');
      final canAuthenticate = await _localAuth.canCheckBiometrics;
      debugPrint('Can authenticate with biometrics: $canAuthenticate');

      final isDeviceSupported = await _localAuth.isDeviceSupported();
      debugPrint('Device supports biometrics: $isDeviceSupported');

      final availableBiometrics = await _localAuth.getAvailableBiometrics();
      debugPrint('Available biometrics: $availableBiometrics');

      final isBiometricEnabled =
          await _secureStorage.read(key: 'biometric_enabled') == 'true';
      debugPrint('Biometric authentication enabled: $isBiometricEnabled');

      state = state.copyWith(
        isBiometricAvailable: canAuthenticate && isDeviceSupported,
        isBiometricEnabled: isBiometricEnabled,
      );

      debugPrint(
          'Biometric state updated - available: ${state.isBiometricAvailable}, enabled: ${state.isBiometricEnabled}');
    } catch (e, stackTrace) {
      debugPrint('Error checking biometric availability: $e');
      debugPrint('Stack trace: $stackTrace');
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign In process...');
      state = state.copyWith(isLoading: true, error: null);

      // Initialize Google Sign In if needed
      await _googleSignIn.initialize();

      // Trigger Google Sign In flow
      debugPrint('Triggering Google Sign In UI...');
      final GoogleSignInAccount googleUser = await _googleSignIn.authenticate();
      debugPrint('Google Sign In successful for user: ${googleUser.email}');

      // Obtain auth details from request
      debugPrint('Getting authentication details...');
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;
      debugPrint('Got authentication tokens');

      // Sign in with Supabase using Google OAuth
      debugPrint('Signing in with Supabase using Google...');
      final response = await _supabase.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: googleAuth.idToken!,
      );

      final user = response.user;
      debugPrint('Supabase sign in completed for user ID: ${user?.id}');

      if (user != null) {
        // Check if user exists in hybrid database
        debugPrint('Checking if user exists in database...');
        final existingUser = await _databaseService.find('users', user.id);

        if (existingUser == null) {
          // Create new user document
          debugPrint('Creating new user document in database...');
          await _databaseService.create('users', {
            'uid': user.id,
            'email': user.email,
            'displayName': user.userMetadata?['full_name'] ?? user.email?.split('@')[0],
            'photoURL': user.userMetadata?['avatar_url'],
            'role': UserRole.buyer.name,
            'is_deleted': false,
          });
          debugPrint('New user document created in database');
        } else {
          debugPrint('User already exists in database');
        }

        debugPrint('Updating auth state with user information');
        state = state.copyWith(
          user: user,
          isEmailVerified: user.emailConfirmedAt != null,
        );
        debugPrint('Google Sign In process completed successfully');
      }
    } catch (e, stackTrace) {
      debugPrint('Error during Google Sign In: $e');
      debugPrint('Stack trace: $stackTrace');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
      debugPrint('Google Sign In process finished');
    }
  }

  /// Sign in with biometrics
  Future<void> signInWithBiometrics() async {
    try {
      debugPrint('Starting biometric authentication...');
      state = state.copyWith(isLoading: true, error: null);

      // Check if biometrics are available
      await _checkBiometricAvailability();

      if (!state.isBiometricAvailable) {
        debugPrint('Biometric authentication is not available');
        throw Exception('Biometric authentication is not available');
      }

      debugPrint('Prompting user for biometric authentication...');
      final authenticated = await _localAuth.authenticate(
        localizedReason: 'Please authenticate to sign in',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );
      debugPrint('Biometric authentication result: $authenticated');

      if (authenticated) {
        // Retrieve stored credentials
        debugPrint('Retrieving stored credentials...');
        final storedEmail = await _secureStorage.read(key: 'email');
        final storedPassword = await _secureStorage.read(key: 'password');
        debugPrint('Stored email found: ${storedEmail != null}');
        debugPrint('Stored password found: ${storedPassword != null}');

        if (storedEmail != null && storedPassword != null) {
          debugPrint('Signing in with stored credentials...');
          await signInWithEmailAndPassword(
            email: storedEmail,
            password: storedPassword,
          );
          debugPrint('Sign in with stored credentials successful');
        } else {
          debugPrint('No stored credentials found');
          throw Exception('No stored credentials found');
        }
      } else {
        debugPrint('User cancelled biometric authentication');
        state = state.copyWith(error: 'Authentication cancelled');
      }
    } catch (e, stackTrace) {
      debugPrint('Error during biometric authentication: $e');
      debugPrint('Stack trace: $stackTrace');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
      debugPrint('Biometric authentication process completed');
    }
  }

  /// Enable/disable biometric authentication
  Future<void> toggleBiometricAuth(bool enable) async {
    try {
      debugPrint('Toggling biometric authentication: enable=$enable');

      if (enable) {
        // Check if biometrics are available
        await _checkBiometricAvailability();

        if (!state.isBiometricAvailable) {
          debugPrint('Biometric authentication is not available');
          throw Exception('Biometric authentication is not available');
        }

        debugPrint('Prompting user to enable biometric authentication...');
        final authenticated = await _localAuth.authenticate(
          localizedReason: 'Enable biometric authentication',
          options: const AuthenticationOptions(
            biometricOnly: true,
            stickyAuth: true,
          ),
        );
        debugPrint('Biometric authentication result: $authenticated');

        if (authenticated) {
          // Store current credentials
              final user = _supabase.auth.currentUser;
          debugPrint('Current user: ${user?.email}');

          if (user != null && user.email != null) {
            debugPrint('Storing credentials for biometric authentication...');
            await _secureStorage.write(key: 'email', value: user.email!);
            await _secureStorage.write(
                key: 'password', value: 'biometric_enabled');
            await _secureStorage.write(key: 'biometric_enabled', value: 'true');

            // Verify storage
            final storedEmail = await _secureStorage.read(key: 'email');
            final storedBiometricEnabled =
                await _secureStorage.read(key: 'biometric_enabled');
            debugPrint('Stored email: $storedEmail');
            debugPrint('Stored biometric_enabled: $storedBiometricEnabled');

            state = state.copyWith(isBiometricEnabled: true);
            debugPrint('Biometric authentication enabled successfully');
          } else {
            debugPrint('No user is currently signed in');
            throw Exception('No user is currently signed in');
          }
        } else {
          debugPrint('User cancelled biometric authentication setup');
        }
      } else {
        debugPrint('Disabling biometric authentication...');
        await _secureStorage.delete(key: 'email');
        await _secureStorage.delete(key: 'password');
        await _secureStorage.delete(key: 'biometric_enabled');

        // Verify deletion
        final storedBiometricEnabled =
            await _secureStorage.read(key: 'biometric_enabled');
        debugPrint('Biometric enabled after deletion: $storedBiometricEnabled');

        state = state.copyWith(isBiometricEnabled: false);
        debugPrint('Biometric authentication disabled successfully');
      }
    } catch (e, stackTrace) {
      debugPrint('Error toggling biometric authentication: $e');
      debugPrint('Stack trace: $stackTrace');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Sign in with Email OTP
  Future<void> signInWithOTP(String email) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabase.auth.signInWithOtp(
        email: email,
      );

      state = state.copyWith(
        error: 'Check your email for the verification code',
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign in with Phone OTP
  Future<void> signInWithPhoneOTP(String phoneNumber) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      await _supabase.auth.signInWithOtp(
        phone: phoneNumber,
      );

      state = state.copyWith(
        error: 'Check your phone for the verification code',
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Verify OTP sign in
  Future<void> verifyOTPSignIn(String email, String otp) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      final response = await _supabase.auth.verifyOTP(
        type: OtpType.email,
        email: email,
        token: otp,
      );

      final user = response.user;
      if (user != null) {
        // Check if user exists in hybrid database
        final existingUser = await _databaseService.find('users', user.id);

        if (existingUser == null) {
          // Create new user document
          await _databaseService.create('users', {
            'uid': user.id,
            'email': user.email,
            'role': UserRole.buyer.name,
            'is_deleted': false,
          });
        }

        state = state.copyWith(
          user: user,
          isEmailVerified: user.emailConfirmedAt != null,
        );
      } else {
        throw Exception('Invalid OTP or email');
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
    bool storeCredentials = true,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      debugPrint('Attempting to sign in with email: $email');

      // Sign in with Supabase
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      final user = response.user;
      if (user == null) {
        throw Exception('Sign in failed - no user returned');
      }

      // Store credentials for automatic re-login
      if (storeCredentials) {
        debugPrint('Storing credentials for automatic re-login');
        await _secureStorage.write(key: 'email', value: email);
        await _secureStorage.write(key: 'password', value: password);
        await _secureStorage.write(
            key: 'auth_timestamp',
            value: DateTime.now().millisecondsSinceEpoch.toString());
      }

      // Update state with user information
      state = state.copyWith(
        user: user,
        isEmailVerified: user.emailConfirmedAt != null,
      );

      debugPrint('Sign in successful for user: ${user.email}');
    } catch (e) {
      debugPrint('Error during sign in: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }



  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String displayName,
    required String phoneNumber,
    required UserRole role,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);

      // Create user with Supabase Auth
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {
          'display_name': displayName,
          'phone_number': phoneNumber,
          'role': role.name,
        },
      );

      final user = response.user;
      if (user == null) {
        throw Exception('User is null after creation');
      }

      // Create user document in hybrid database
      await _databaseService.create('users', {
        'uid': user.id,
        'email': email,
        'displayName': displayName,
        'phoneNumber': phoneNumber,
        'role': role.name,
        'is_deleted': false,
      });

      state = state.copyWith(
        user: user,
        isEmailVerified: user.emailConfirmedAt != null,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    String? displayName,
    String? phoneNumber,
    String? photoURL,
  }) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      // Update Supabase user metadata
      final updateData = <String, dynamic>{};
      if (displayName != null) updateData['display_name'] = displayName;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (photoURL != null) updateData['avatar_url'] = photoURL;

      if (updateData.isNotEmpty) {
        await _supabase.auth.updateUser(
          UserAttributes(data: updateData),
        );
      }

      // Update hybrid database document
      final updates = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };
      if (displayName != null) updates['displayName'] = displayName;
      if (phoneNumber != null) updates['phoneNumber'] = phoneNumber;
      if (photoURL != null) updates['photoURL'] = photoURL;

      await _databaseService.update('users', user.id, updates);

      state = state.copyWith(
        user: user,
      );
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }



  /// Send email verification
  Future<void> sendEmailVerification() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      // Supabase handles email verification differently
      // This would typically be done during sign up
      await _supabase.auth.resend(
        type: OtpType.signup,
        email: user.email!,
      );

      state = state.copyWith(isEmailVerified: user.emailConfirmedAt != null);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }



  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      await _supabase.auth.resetPasswordForEmail(email);
      state = state.copyWith(error: null);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  /// Sign out
  ///
  /// If [clearCredentials] is true, all stored credentials will be deleted
  /// and the user will need to log in again next time.
  ///
  /// If [clearCredentials] is false, the credentials will be kept and the
  /// app will attempt to automatically log in the user next time.
  Future<void> signOut({bool clearCredentials = false}) async {
    try {
      debugPrint('Signing out user with clearCredentials=$clearCredentials');
      state = state.copyWith(isLoading: true, error: null);

      if (clearCredentials) {
        // Clear all stored credentials
        debugPrint('Clearing all stored credentials');
        await _secureStorage.delete(key: 'email');
        await _secureStorage.delete(key: 'password');
        await _secureStorage.delete(key: 'refresh_token');
        await _secureStorage.delete(key: 'auth_timestamp');

        // Also clear biometric authentication if enabled
        if (state.isBiometricEnabled) {
          await _secureStorage.delete(key: 'biometric_enabled');
          state = state.copyWith(isBiometricEnabled: false);
        }

        // Sign out from all providers
        await _googleSignIn.signOut();
      }

      // Always sign out from Supabase Auth
      await _supabase.auth.signOut();

      // Reset state
      state = const AuthState();
      debugPrint('Sign out completed successfully');
    } catch (e) {
      debugPrint('Error during sign out: $e');
      state = state.copyWith(error: e.toString());
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }
}
