import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';
import '../../../../shared/services/storage/adaptive_storage_service.dart';
import '../../../../shared/utils/logger.dart';
import '../../../../shared/ui_components/buttons/app_button.dart';
import '../../../../shared/ui_components/inputs/text_field.dart';
import '../../../../shared/widgets/auth/email_verification_widget.dart';
import '../../providers/saviour_auth_provider.dart';
import '../../saviour_routes.dart';
import '../../widgets/vehicle_type_selector.dart';

final _logger = getLogger('SaviourRegisterScreen');

// Set to true to bypass document upload requirements during development
const _debugSkipDocumentUploads = true;

class RegisterScreen extends ConsumerStatefulWidget {
  const RegisterScreen({super.key});

  @override
  ConsumerState<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends ConsumerState<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _vehicleNumberController = TextEditingController();
  final _vehicleBrandController = TextEditingController();
  final _vehicleModelController = TextEditingController();
  final _vehicleYearController = TextEditingController();
  final _vehicleColorController = TextEditingController();
  final _licensePlateController = TextEditingController();
  String? _selectedVehicleType;

  // Document files
  File? _aadharDocument;
  File? _drivingLicenseDocument;
  File? _vehicleDocument;
  bool _isUploadingDocuments = false;

  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _agreeToTerms = false;
  bool _isEmailVerified = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _vehicleNumberController.dispose();
    _vehicleBrandController.dispose();
    _vehicleModelController.dispose();
    _vehicleYearController.dispose();
    _vehicleColorController.dispose();
    _licensePlateController.dispose();
    super.dispose();
  }

  // Upload document to hybrid storage
  Future<String?> _uploadDocument(File file, String type) async {
    // If in debug mode with document uploads disabled, return a placeholder URL
    if (_debugSkipDocumentUploads) {
      _logger.info('DEBUG MODE: Skipping actual document upload for $type');
      return 'https://placeholder.com/${type}_document.jpg';
    }

    try {
      setState(() {
        _isUploadingDocuments = true;
      });

      // Get the adaptive storage service
      final storageService = ref.read(adaptiveStorageServiceProvider);

      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final storagePath = 'documents/delivery_partner_documents/${_emailController.text.trim()}/${type}_$fileName';

      final downloadUrl = await storageService.uploadFile(
        filePath: file.path,
        storagePath: storagePath,
      );

      _logger.info('Document uploaded successfully: $type -> $downloadUrl');

      return downloadUrl;
    } catch (e) {
      _logger.severe('Error uploading $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to upload $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    } finally {
      setState(() {
        _isUploadingDocuments = false;
      });
    }
  }

  // Pick document file
  Future<void> _pickDocument(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        setState(() {
          if (type == 'aadhar') {
            _aadharDocument = file;
          } else if (type == 'license') {
            _drivingLicenseDocument = file;
          } else if (type == 'vehicle') {
            _vehicleDocument = file;
          }
        });
      }
    } catch (e) {
      _logger.severe('Error picking $type document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick $type document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _register() async {
    if (!_formKey.currentState!.validate()) return;

    if (!_isEmailVerified) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please verify your email address first'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please agree to the terms and conditions'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Check if documents are selected (skip in debug mode)
    if (!_debugSkipDocumentUploads) {
      if (_aadharDocument == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload your Aadhar document'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_drivingLicenseDocument == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload your Driving License document'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      if (_vehicleDocument == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please upload your Vehicle Registration document'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }
    } else {
      _logger.info('DEBUG MODE: Skipping document validation');
    }

    try {
      setState(() {
        _isUploadingDocuments = true;
      });

      // Document URLs
      String? aadharUrl;
      String? licenseUrl;
      String? vehicleUrl;

      // In debug mode, use placeholder URLs instead of actual uploads
      if (_debugSkipDocumentUploads) {
        _logger.info('DEBUG MODE: Using placeholder document URLs');

        // Use placeholder URLs for documents in debug mode
        aadharUrl = 'https://placeholder.com/aadhar_document.jpg';
        licenseUrl = 'https://placeholder.com/license_document.jpg';
        vehicleUrl = 'https://placeholder.com/vehicle_document.jpg';
      } else {
        // Normal document upload process
        aadharUrl = await _uploadDocument(_aadharDocument!, 'aadhar');
        licenseUrl = await _uploadDocument(_drivingLicenseDocument!, 'license');
        vehicleUrl = await _uploadDocument(_vehicleDocument!, 'vehicle');

        if (aadharUrl == null || licenseUrl == null || vehicleUrl == null) {
          setState(() {
            _isUploadingDocuments = false;
          });
          return;
        }
      }

      // Register user with document URLs
      await ref.read(saviourAuthStateProvider.notifier).registerWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        vehicleType: _selectedVehicleType ?? '',
        vehicleNumber: _vehicleNumberController.text.trim(),
        aadharNumber: aadharUrl,
        drivingLicense: licenseUrl,
        vehicleDocument: vehicleUrl,
        vehicleBrand: _vehicleBrandController.text.trim(),
        vehicleModel: _vehicleModelController.text.trim(),
        vehicleYear: _vehicleYearController.text.trim(),
        vehicleColor: _vehicleColorController.text.trim(),
        licensePlate: _licensePlateController.text.trim(),
      );

      // Check auth state after registration
      final authState = ref.read(saviourAuthStateProvider);
      _logger.info('Auth state after registration: isPendingApproval=${authState.isPendingApproval}');

      setState(() {
        _isUploadingDocuments = false;
      });

      if (mounted) {
        if (authState.isPendingApproval) {
          // Navigate to pending approval screen
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Registration successful! Please wait for admin approval.'),
              backgroundColor: Colors.green,
            ),
          );
          context.go(SaviourRoutes.pendingApproval);
        } else if (authState.errorMessage != null) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authState.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        } else {
          // Navigate to login screen as fallback
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Registration successful! Please wait for admin approval.'),
              backgroundColor: Colors.green,
            ),
          );
          context.go(SaviourRoutes.login);
        }
      }
    } catch (e) {
      setState(() {
        _isUploadingDocuments = false;
      });

      _logger.severe('Error during registration: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Registration failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(saviourAuthStateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Register as Delivery Partner'),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Title
                Text(
                  'Create Your Account',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),

                // Subtitle
                Text(
                  'Fill in your details to register as a delivery partner',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 24),

                // Error message
                if (authState.errorMessage != null) ...[
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red[200]!),
                    ),
                    child: Text(
                      authState.errorMessage!,
                      style: TextStyle(color: Colors.red[800]),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Personal Information Section
                _buildSectionHeader('Personal Information'),
                const SizedBox(height: 16),

                // Name field
                AppTextField(
                  controller: _nameController,
                  label: 'Full Name',
                  hint: 'Enter your full name',
                  prefixIcon: const Icon(Icons.person_outline),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Email verification field
                EmailVerificationWidget(
                  emailController: _emailController,
                  onVerificationChanged: (isVerified) {
                    setState(() {
                      _isEmailVerified = isVerified;
                    });
                  },
                  primaryColor: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),

                // Phone field
                AppTextField(
                  controller: _phoneController,
                  label: 'Phone Number',
                  hint: 'Enter your phone number',
                  keyboardType: TextInputType.phone,
                  prefixIcon: const Icon(Icons.phone_outlined),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your phone number';
                    }
                    if (!RegExp(r'^\+?[0-9]{10,15}$').hasMatch(value)) {
                      return 'Please enter a valid phone number';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Vehicle Information Section
                _buildSectionHeader('Vehicle Information'),
                const SizedBox(height: 16),

                // Vehicle type dropdown
                VehicleTypeDropdown(
                  selectedVehicleType: _selectedVehicleType,
                  onChanged: (value) {
                    setState(() {
                      _selectedVehicleType = value;
                    });
                  },
                  label: 'Vehicle Type',
                  hint: 'Select your vehicle type',
                  isRequired: true,
                ),
                const SizedBox(height: 16),

                // Vehicle number field
                AppTextField(
                  controller: _vehicleNumberController,
                  label: 'Vehicle Number',
                  hint: 'Enter your vehicle registration number',
                  prefixIcon: const Icon(Icons.pin_outlined),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your vehicle number';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Vehicle brand field
                AppTextField(
                  controller: _vehicleBrandController,
                  label: 'Vehicle Brand',
                  hint: 'Enter vehicle brand (e.g., Honda, Bajaj)',
                  prefixIcon: const Icon(Icons.branding_watermark_outlined),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your vehicle brand';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Vehicle model field
                AppTextField(
                  controller: _vehicleModelController,
                  label: 'Vehicle Model',
                  hint: 'Enter vehicle model (e.g., Activa, Pulsar)',
                  prefixIcon: const Icon(Icons.model_training_outlined),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your vehicle model';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Vehicle year and color in a row
                Row(
                  children: [
                    Expanded(
                      child: AppTextField(
                        controller: _vehicleYearController,
                        label: 'Year',
                        hint: 'e.g., 2020',
                        keyboardType: TextInputType.number,
                        prefixIcon: const Icon(Icons.calendar_today_outlined),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter year';
                          }
                          final year = int.tryParse(value);
                          if (year == null || year < 1990 || year > DateTime.now().year + 1) {
                            return 'Enter valid year';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: AppTextField(
                        controller: _vehicleColorController,
                        label: 'Color',
                        hint: 'e.g., Red, Blue',
                        prefixIcon: const Icon(Icons.palette_outlined),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter color';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // License plate field
                AppTextField(
                  controller: _licensePlateController,
                  label: 'License Plate',
                  hint: 'Enter license plate number',
                  prefixIcon: const Icon(Icons.confirmation_number_outlined),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter license plate number';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Vehicle document upload
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Vehicle Registration Document',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => _pickDocument('vehicle'),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.upload_file, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                _vehicleDocument != null
                                    ? _vehicleDocument!.path.split('/').last
                                    : 'Upload Vehicle Registration Document',
                                style: TextStyle(
                                  color: _vehicleDocument != null ? Colors.black : Colors.grey[600],
                                ),
                              ),
                            ),
                            if (_vehicleDocument != null)
                              const Icon(Icons.check_circle, color: Colors.green),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Document Information Section
                _buildSectionHeader('Document Information'),
                if (_debugSkipDocumentUploads) ...[
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.amber[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.amber),
                    ),
                    child: const Row(
                      children: [
                        Icon(Icons.info_outline, color: Colors.amber),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Debug mode: Document uploads are disabled. Placeholder URLs will be used.',
                            style: TextStyle(color: Colors.amber, fontWeight: FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 16),

                // Aadhar document upload
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Aadhar Card',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => _pickDocument('aadhar'),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.upload_file, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                _aadharDocument != null
                                    ? _aadharDocument!.path.split('/').last
                                    : 'Upload Aadhar Card',
                                style: TextStyle(
                                  color: _aadharDocument != null ? Colors.black : Colors.grey[600],
                                ),
                              ),
                            ),
                            if (_aadharDocument != null)
                              const Icon(Icons.check_circle, color: Colors.green),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Driving license document upload
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Driving License',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    InkWell(
                      onTap: () => _pickDocument('license'),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.upload_file, color: Colors.green),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                _drivingLicenseDocument != null
                                    ? _drivingLicenseDocument!.path.split('/').last
                                    : 'Upload Driving License',
                                style: TextStyle(
                                  color: _drivingLicenseDocument != null ? Colors.black : Colors.grey[600],
                                ),
                              ),
                            ),
                            if (_drivingLicenseDocument != null)
                              const Icon(Icons.check_circle, color: Colors.green),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Account Security Section
                _buildSectionHeader('Account Security'),
                const SizedBox(height: 16),

                // Password field
                AppTextField(
                  controller: _passwordController,
                  label: 'Password',
                  hint: 'Create a password',
                  obscureText: !_isPasswordVisible,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a password';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Confirm password field
                AppTextField(
                  controller: _confirmPasswordController,
                  label: 'Confirm Password',
                  hint: 'Confirm your password',
                  obscureText: !_isConfirmPasswordVisible,
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _isConfirmPasswordVisible ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                      });
                    },
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please confirm your password';
                    }
                    if (value != _passwordController.text) {
                      return 'Passwords do not match';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Terms and conditions
                Row(
                  children: [
                    Checkbox(
                      value: _agreeToTerms,
                      onChanged: (value) {
                        setState(() {
                          _agreeToTerms = value ?? false;
                        });
                      },
                    ),
                    Expanded(
                      child: Text(
                        'I agree to the Terms of Service and Privacy Policy',
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Register button
                AppButton(
                  onPressed: _register,
                  isLoading: authState.isLoading || _isUploadingDocuments,
                  child: const Text('Register'),
                ),
                const SizedBox(height: 24),

                // Login link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Already have an account?'),
                    TextButton(
                      onPressed: () {
                        context.go(SaviourRoutes.login);
                      },
                      child: const Text('Sign In'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        const Divider(),
      ],
    );
  }
}

